// ! need to fix type, refactor this file

/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/require-await */
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
// Fake services for now
const quizAttemptService = {
  startQuizAttempt: async () => ({ data: {} }),
  navigateToQuestion: async () => ({ data: { question: {}, attemptAnswer: {} } }),
  getActiveQuizAttempt: async () => ({ data: {} }),
  saveAnswer: async () => {},
  submitQuizAttempt: async () => ({ data: {} }),
};
const quizDashboardService = {
  getQuizSummary: async () => ({}),
  getUserAttempts: async () => ({}),
  getQuizAnswerSummary: async () => ({}),
};
import { useQuasar } from 'quasar';

export const useQuizStore = defineStore('quiz', () => {
  // --- Attempt State ---
  const currentAttempt = ref<any>(null);
  const currentQuestion = ref<any>(null);
  const currentAttemptAnswer = ref<any>(null);
  const submitResult = ref<any>(null);
  const loading = ref(false);

  // --- Dashboard State ---
  const $q = useQuasar();
  const currentQuizId = ref<number | null>(null);
  const quizSummary = ref<any>(null);
  const userAttempts = ref<any>(null);
  const answerSummary = ref<any>(null);

  const isLoadingSummary = ref(false);
  const isLoadingUserAttempts = ref(false);
  const isLoadingAnswerSummary = ref(false);

  const errorSummary = ref<string | null>(null);
  const errorUserAttempts = ref<string | null>(null);
  const errorAnswerSummary = ref<string | null>(null);

  const hasQuizData = computed(() => !!quizSummary.value);
  const overallLoading = computed(
    () =>
      isLoadingSummary.value ||
      isLoadingUserAttempts.value ||
      isLoadingAnswerSummary.value ||
      loading.value,
  );

  // --- Attempt Actions ---
  const startAttempt = async (userId: number, quizId: number) => {
    loading.value = true;
    try {
      const { data } = await quizAttemptService.startQuizAttempt(userId, quizId);
      currentAttempt.value = data;
      const navigateToQuestionData = await quizAttemptService.navigateToQuestion(data.id, 1);
      currentQuestion.value = navigateToQuestionData.data.question;
      currentAttemptAnswer.value = navigateToQuestionData.data.attemptAnswer;
    } finally {
      loading.value = false;
    }
  };

  const loadActiveAttempt = async () => {
    loading.value = true;
    try {
      const { data } = await quizAttemptService.getActiveQuizAttempt();
      currentAttempt.value = data;
    } finally {
      loading.value = false;
    }
  };

  const loadQuestion = async (sequence: number) => {
    if (!currentAttempt.value) return;
    loading.value = true;
    try {
      const { data } = await quizAttemptService.navigateToQuestion(
        currentAttempt.value.id,
        sequence,
      );
      currentQuestion.value = data.question;
      currentAttemptAnswer.value = data.attemptAnswer;
    } finally {
      loading.value = false;
    }
  };

  const saveAnswer = async (answer: unknown) => {
    if (!currentAttempt.value) return;
    await quizAttemptService.saveAnswer(currentAttempt.value.id, answer);
    currentAttemptAnswer.value = answer;
  };

  const submitAttempt = async () => {
    if (!currentAttempt.value) return;
    const { data } = await quizAttemptService.submitQuizAttempt(currentAttempt.value.id);
    submitResult.value = data;
  };

  // --- Dashboard Actions ---
  function setQuizId(id: number | null) {
    if (currentQuizId.value !== id) {
      currentQuizId.value = id;
      quizSummary.value = null;
      userAttempts.value = null;
      answerSummary.value = null;
      errorSummary.value = null;
      errorUserAttempts.value = null;
      errorAnswerSummary.value = null;
    }
  }

  async function fetchQuizSummary(quizIdToFetch?: number) {
    const id = quizIdToFetch || currentQuizId.value;
    if (id === null) {
      errorSummary.value = 'Quiz ID is not set.';
      $q.notify({ type: 'warning', message: errorSummary.value });
      return;
    }
    isLoadingSummary.value = true;
    errorSummary.value = null;
    try {
      quizSummary.value = await quizDashboardService.getQuizSummary(id);
    } catch (err) {
      errorSummary.value =
        err?.response?.data?.message || err?.message || 'Failed to load quiz summary.';
      $q.notify({ type: 'negative', message: errorSummary.value || 'Error loading quiz summary' });
      quizSummary.value = null;
    } finally {
      isLoadingSummary.value = false;
    }
  }

  async function fetchUserAttempts(params: unknown = {}, quizIdToFetch?: number) {
    const id = quizIdToFetch || currentQuizId.value;
    if (id === null) {
      errorUserAttempts.value = 'Quiz ID is not set for fetching user attempts.';
      $q.notify({ type: 'warning', message: errorUserAttempts.value });
      return;
    }
    isLoadingUserAttempts.value = true;
    errorUserAttempts.value = null;
    try {
      userAttempts.value = await quizDashboardService.getUserAttempts(id, params);
    } catch (err) {
      errorUserAttempts.value =
        err?.response?.data?.message || err?.message || 'Failed to load user attempts.';
      $q.notify({
        type: 'negative',
        message: errorUserAttempts.value || 'Error loading user attempts',
      });
      userAttempts.value = null;
    } finally {
      isLoadingUserAttempts.value = false;
    }
  }

  async function fetchAnswerSummary(quizIdToFetch?: number) {
    const id = quizIdToFetch || currentQuizId.value;
    if (id === null) {
      errorAnswerSummary.value = 'Quiz ID is not set for fetching answer summary.';
      $q.notify({ type: 'warning', message: errorAnswerSummary.value });
      return;
    }
    isLoadingAnswerSummary.value = true;
    errorAnswerSummary.value = null;
    try {
      answerSummary.value = await quizDashboardService.getQuizAnswerSummary(id);
    } catch (err) {
      errorAnswerSummary.value =
        err?.response?.data?.message || err?.message || 'Failed to load answer summary.';
      $q.notify({
        type: 'negative',
        message: errorAnswerSummary.value || 'Error loading answer summary',
      });
      answerSummary.value = null;
    } finally {
      isLoadingAnswerSummary.value = false;
    }
  }

  async function fetchAllQuizDataForCurrentId() {
    if (currentQuizId.value === null) {
      console.warn('Cannot fetch all quiz data, Quiz ID is not set.');
      return;
    }
    await Promise.all([
      fetchQuizSummary(currentQuizId.value),
      fetchUserAttempts({}, currentQuizId.value),
      fetchAnswerSummary(currentQuizId.value),
    ]);
  }

  // --- Reset ---
  const resetStore = () => {
    currentAttempt.value = null;
    currentQuestion.value = null;
    currentAttemptAnswer.value = null;
    submitResult.value = null;
    loading.value = false;
    currentQuizId.value = null;
    quizSummary.value = null;
    userAttempts.value = null;
    answerSummary.value = null;
    isLoadingSummary.value = false;
    isLoadingUserAttempts.value = false;
    isLoadingAnswerSummary.value = false;
    errorSummary.value = null;
    errorUserAttempts.value = null;
    errorAnswerSummary.value = null;
  };

  return {
    // Attempt State
    currentAttempt,
    currentQuestion,
    currentAttemptAnswer,
    submitResult,
    loading,
    startAttempt,
    loadActiveAttempt,
    loadQuestion,
    saveAnswer,
    submitAttempt,
    // Dashboard State
    currentQuizId,
    quizSummary,
    userAttempts,
    answerSummary,
    isLoadingSummary,
    isLoadingUserAttempts,
    isLoadingAnswerSummary,
    errorSummary,
    errorUserAttempts,
    errorAnswerSummary,
    hasQuizData,
    overallLoading,
    setQuizId,
    fetchQuizSummary,
    fetchUserAttempts,
    fetchAnswerSummary,
    fetchAllQuizDataForCurrentId,
    // Reset
    resetStore,
  };
});
