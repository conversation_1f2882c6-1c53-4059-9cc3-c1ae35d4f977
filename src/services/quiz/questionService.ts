import type { Option, Question } from 'src/types/models';
import { api } from 'src/boot/axios';
import { Notify } from 'quasar';
import { useGlobalStore } from 'src/stores/global';
const globalStore = useGlobalStore();
const showError = (message: string) => {
  Notify.create({
    message,
    type: 'negative',
    position: 'bottom',
    timeout: 3000,
  });
};
// สร้างคำถามใหม่
const createQuestion = async (questionData: Partial<Question>): Promise<{ data: Question }> => {
  try {
    globalStore.Loading();
    return await api.post('/question', questionData);
  } catch {
    showError('ไม่สามารถสร้างคำถามได้');
    throw new Error('Create question failed');
  }
};

// ดึงคำถามทั้งหมด (มีตัวกรอง quizId)
const getQuestions = async (quizId?: number): Promise<{ data: Question[] }> => {
  try {
    return await api.get('/question', { params: { quizId } });
  } catch {
    showError('ไม่สามารถโหลดรายการคำถามได้');
    throw new Error('Get questions failed');
  }
};

// ดึงคำถามตาม ID
const getQuestionById = async (id: number): Promise<{ data: Question }> => {
  try {
    return await api.get(`/question/${id}`);
  } catch {
    showError('ไม่สามารถโหลดคำถามนี้ได้');
    throw new Error('Get question by ID failed');
  }
};

// ลบคำถามจาก Quiz
const deleteQuestion = async (quizId: number, questionId: number): Promise<void> => {
  try {
    await api.delete(`/question/quiz/${quizId}/question/${questionId}`);
    Notify.create({ message: 'ลบเรียบร้อยแล้ว', type: 'positive' });
  } catch {
    showError('ไม่สามารถลบคำถามได้');
    throw new Error('Delete question failed');
  }
};

// จัดเรียงคำถามใหม่
interface QuestionOrder {
  questionId: number;
  sequence: number;
}

const reorderQuestions = async (
  quizId: number,
  questionOrders: QuestionOrder[],
): Promise<{ data: Question[] }> => {
  try {
    globalStore.Loading();
    return await api.patch(`/question/quiz/${quizId}/reorder`, questionOrders);
  } catch {
    showError('ไม่สามารถจัดเรียงคำถามใหม่ได้');
    throw new Error('Reorder questions failed');
  }
};

// อัปเดตฟิลด์เดียวของคำถาม
const updateQuestionField = async (
  id: number,
  field: string,
  value: unknown,
): Promise<{ data: Question }> => {
  try {
    globalStore.Loading();
    return await api.patch(`/question/${id}/field`, { field, value });
  } catch {
    showError('ไม่สามารถอัปเดตคำถามได้');
    throw new Error('Update question field failed');
  }
};

// อัปเดตฟิลด์เดียวของตัวเลือก
const updateChoiceField = async (
  questionId: number,
  choiceId: number,
  field: string,
  value: unknown,
): Promise<{ data: Option }> => {
  try {
    globalStore.Loading();
    return await api.patch(`/question/${questionId}/choice/${choiceId}`, { field, value });
  } catch {
    showError('ไม่สามารถอัปเดตตัวเลือกได้');
    throw new Error('Update choice field failed');
  }
};

// เพิ่มตัวเลือกให้คำถาม
const addChoice = async (
  questionId: number,
  field: string,
  value: unknown,
): Promise<{ data: Option }> => {
  try {
    globalStore.Loading();
    return await api.post(`/question/${questionId}/choice`, { field, value });
  } catch {
    showError('ไม่สามารถเพิ่มตัวเลือกได้');
    throw new Error('Add choice failed');
  }
};

// ลบตัวเลือกออกจากคำถาม
const removeChoice = async (questionId: number, choiceId: number): Promise<void> => {
  try {
    await api.delete(`/question/${questionId}/choice/${choiceId}`);
    Notify.create({ message: 'ลบเรียบร้อยแล้ว', type: 'positive' });
  } catch {
    showError('ไม่สามารถลบตัวเลือกได้');
    throw new Error('Remove choice failed');
  }
};

export default {
  createQuestion,
  getQuestions,
  getQuestionById,
  deleteQuestion,
  reorderQuestions,
  updateQuestionField,
  updateChoiceField,
  addChoice,
  removeChoice,
};
