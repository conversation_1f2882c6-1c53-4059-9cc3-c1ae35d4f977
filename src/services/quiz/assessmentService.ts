import { api as axios } from 'src/boot/axios';
import type { QTableProps } from 'quasar';
import type { DataResponse } from 'src/types/data';
import type { Assessment, ItemBlock, HeaderBody, Question, Option } from 'src/types/models';
import { formatParams } from 'src/utils/utils';
import { Notify } from 'quasar';
import { useGlobalStore } from 'src/stores/global';
const globalStore = useGlobalStore();
type AssessmentType = 'quiz' | 'evaluate';

const showError = (message: string) => {
  Notify.create({
    message,
    type: 'negative',
    position: 'bottom',
    timeout: 3000,
  });
};
export class AssessmentService {
  path = '';
  headerBodyPath = '';

  constructor(path: AssessmentType) {
    this.path = path + '/assessments';
    this.headerBodyPath = path + '/header-bodies';
  }

  async fetchAll(pag: QTableProps['pagination'], search?: string) {
    try {
      const params = formatParams(pag, search);
      const res = await axios.get<DataResponse<Assessment>>(this.path, { params });
      return res.data;
    } catch {
      showError('ไม่สามารถโหลดรายการแบบประเมินได้');
      throw new Error('Fetch assessments failed');
    }
  }

  async fetchOne(id: number) {
    try {
      const res = await axios.get<Assessment>(`${this.path}/${id}`);
      return res.data;
    } catch {
      showError('ไม่สามารถโหลดข้อมูลแบบประเมินได้');
      throw new Error('Fetch single assessment failed');
    }
  }

  async createOne(assessment: Partial<Assessment>) {
    try {
      const res = await axios.post<Assessment>(this.path, assessment);
      globalStore.Loading();
      return res.data;
    } catch {
      showError('ไม่สามารถสร้างแบบประเมินได้');
      throw new Error('Create assessment failed');
    }
  }

  async updateOne(id: number) {
    try {
      const res = await axios.patch<Assessment>(`${this.path}/${id}`);
      globalStore.Loading();
      return res.data;
    } catch {
      showError('ไม่สามารถอัปเดตแบบประเมินได้');
      throw new Error('Update assessment failed');
    }
  }

  async deleteOne(id: number) {
    try {
      const res = await axios.delete<Assessment>(`${this.path}/${id}`);
      Notify.create({ message: 'ลบเรียบร้อยแล้ว', type: 'positive' });
      return res.data;
    } catch {
      showError('ไม่สามารถลบแบบประเมินได้');
      throw new Error('Delete assessment failed');
    }
  }

  async createBlock(block: Omit<ItemBlock, 'id'>): Promise<ItemBlock | undefined> {
    // Use the correct endpoint directly based on the type
    if (this.path.includes('evaluate')) {
      // For evaluate type, use the correct endpoint: /evaluate/item-blocks/block
      return await this.createBlockViaCorrectEndpoint(block);
    } else {
      // For quiz type, try the original endpoint first
      try {
        console.log('Attempting to create block with endpoint:', `${this.path}/item-blocks`);
        console.log('Block data:', block);

        const res = await axios.post<ItemBlock>(`${this.path}/item-blocks`, block);
        globalStore.Loading();
        return res.data;
      } catch (error: unknown) {
        console.error('Block creation failed:', error);

        // Type guard for axios error
        if (error && typeof error === 'object' && 'response' in error) {
          const axiosError = error as { response?: { data?: unknown; status?: number } };
          console.error('Error response:', axiosError.response?.data);
          console.error('Error status:', axiosError.response?.status);

          // Check if it's a 404 error and try alternative endpoints
          if (axiosError.response?.status === 404) {
            console.log('Primary endpoint failed, trying alternative approaches...');
            return await this.createBlockFallback(block);
          } else {
            showError('ไม่สามารถสร้าง Block ได้');
          }
        } else {
          console.error('Full error:', error);
          showError('ไม่สามารถสร้าง Block ได้');
        }
        return;
      }
    }
  }

  // Fallback method to try alternative approaches
  private async createBlockFallback(block: Omit<ItemBlock, 'id'>): Promise<ItemBlock | undefined> {
    // First try the simple /evaluate/item-blocks endpoint
    try {
      console.log('Trying simple evaluate endpoint: /evaluate/item-blocks');
      const payload = {
        assessmentId: block.assessmentId,
        type: block.type || 'RADIO',
        sequence: block.sequence,
        section: block.section,
        isRequired: block.isRequired,
      };
      console.log('Payload for simple endpoint:', payload);

      const res = await axios.post<ItemBlock>('/evaluate/item-blocks', payload);
      console.log('Success with simple endpoint:', res.data);
      globalStore.Loading();
      return res.data;
    } catch (error) {
      console.error('Simple evaluate endpoint failed:', error);
    }

    console.log('Trying correct evaluate endpoint...');

    try {
      // Try the correct evaluate endpoint: /evaluate/item-blocks/block
      return await this.createBlockViaCorrectEndpoint(block);
    } catch (error) {
      console.error('Correct evaluate endpoint failed:', error);
    }

    console.log('Trying assessment update approach...');

    try {
      // Try using the assessment update approach as backup
      return await this.createBlockViaAssessmentUpdate(block);
    } catch (error) {
      console.error('Assessment update approach failed:', error);
    }

    // Try other alternative endpoints as last resort
    const alternatives = [
      `/evaluate/item-blocks`,
      `/item-blocks`,
      `/evaluate/item-blocks/create`,
      `/evaluate/blocks`,
      `/evaluate/blocks/create`,
      `${this.path.replace('/assessments', '')}/item-blocks`,
      `${this.path.replace('/assessments', '')}/blocks`,
    ];

    for (const endpoint of alternatives) {
      try {
        console.log(`Trying alternative endpoint: ${endpoint}`);
        const res = await axios.post<ItemBlock>(endpoint, block);
        console.log(`Success with endpoint: ${endpoint}`);
        globalStore.Loading();
        return res.data;
      } catch {
        console.log(`Failed with endpoint: ${endpoint}`);
        continue;
      }
    }

    // If all alternatives fail, show error
    showError(`ไม่พบ endpoint ที่ใช้งานได้สำหรับการสร้าง Block - กรุณาตรวจสอบ backend API`);
    return;
  }

  // Try creating block using the correct evaluate endpoint
  private async createBlockViaCorrectEndpoint(
    block: Omit<ItemBlock, 'id'>,
  ): Promise<ItemBlock | undefined> {
    try {
      console.log('Attempting to create block via correct endpoint: /evaluate/item-blocks/block');

      // Prepare the payload with required fields
      const payload = {
        assessmentId: block.assessmentId,
        type: block.type || 'RADIO', // Default to 'RADIO' as specified
        // Include other fields that might be needed
        sequence: block.sequence,
        section: block.section,
        isRequired: block.isRequired,
      };

      console.log('Payload for correct endpoint:', payload);

      const res = await axios.post<ItemBlock>('/evaluate/item-blocks/block', payload);
      console.log('Success with correct endpoint:', res.data);
      globalStore.Loading();
      return res.data;
    } catch (error: unknown) {
      console.error('Correct endpoint failed:', error);

      // Type guard for axios error
      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as { response?: { data?: unknown; status?: number } };
        console.error('Error response from correct endpoint:', axiosError.response?.data);
        console.error('Error status from correct endpoint:', axiosError.response?.status);
      }

      // Try fallback approaches
      console.log('Trying fallback approaches...');
      return await this.createBlockFallback(block);
    }
  }

  // Try creating block by updating the assessment with new blocks
  private async createBlockViaAssessmentUpdate(
    block: Omit<ItemBlock, 'id'>,
  ): Promise<ItemBlock | undefined> {
    if (!block.assessmentId) {
      throw new Error('Assessment ID is required for block creation');
    }

    console.log('Attempting to create block via assessment update...');

    // First, get the current assessment
    const currentAssessment = await this.fetchOne(block.assessmentId);
    console.log('Current assessment:', currentAssessment);

    // Create a new block with a temporary ID (backend should assign real ID)
    const tempId = Date.now(); // Temporary ID
    const newBlock: ItemBlock = {
      id: tempId,
      ...block,
    };

    // Add the new block to the assessment
    const updatedItemBlocks = [...(currentAssessment.itemBlocks || []), newBlock];

    const assessmentUpdate = {
      ...currentAssessment,
      itemBlocks: updatedItemBlocks,
    };

    console.log('Updating assessment with new block:', assessmentUpdate);

    // Update the assessment with the new block
    const updatedAssessment = await axios.patch<Assessment>(
      `${this.path}/${block.assessmentId}`,
      assessmentUpdate,
    );

    // Find the newly created block in the response
    const createdBlock = updatedAssessment.data.itemBlocks?.find(
      (b) => b.sequence === block.sequence && b.section === block.section && b.type === block.type,
    );

    if (createdBlock) {
      console.log('Block created successfully via assessment update:', createdBlock);
      globalStore.Loading();
      return createdBlock;
    } else {
      throw new Error('Block was not found in updated assessment');
    }
  }

  async addBlock(block: ItemBlock): Promise<ItemBlock | undefined> {
    try {
      const res = await axios.put<ItemBlock>(`${this.path}/item-blocks/${block.id}`, block);
      globalStore.Loading();
      return res.data;
    } catch {
      showError('ไม่สามารถเพิ่ม Block ได้');
      return;
    }
  }

  async updateBlock(block: ItemBlock): Promise<ItemBlock | undefined> {
    try {
      const res = await axios.patch<ItemBlock>(`${this.path}/item-blocks/${block.id}`, block);
      globalStore.Loading();
      return res.data;
    } catch {
      showError('ไม่สามารถอัปเดต Block ได้');
      return;
    }
  }

  async deleteBlock(block: ItemBlock): Promise<ItemBlock | undefined> {
    try {
      const res = await axios.delete<ItemBlock>(`${this.path}/item-blocks/${block.id}`);
      Notify.create({ message: 'ลบเรียบร้อยแล้ว', type: 'positive' });
      return res.data;
    } catch {
      showError('ไม่สามารถลบ Block ได้');
      return;
    }
  }

  // HeaderBody methods moved from HeaderBodyService
  async fetchHeaderBody(id: number): Promise<HeaderBody | undefined> {
    try {
      const res = await axios.get<HeaderBody>(`${this.headerBodyPath}/${id}`);
      return res.data;
    } catch {
      showError('ไม่สามารถโหลดข้อมูล Header Body ได้');
      return;
    }
  }

  async createHeaderBody(headerBody: Partial<HeaderBody>): Promise<HeaderBody | undefined> {
    try {
      const res = await axios.post<HeaderBody>(this.headerBodyPath, headerBody);
      globalStore.Loading();
      console.log('HeaderBody created:', res.data);
      return res.data;
    } catch {
      showError('ไม่สามารถสร้าง Header Body ได้');
      return;
    }
  }

  async updateHeaderBody(
    id: number,
    headerBody: Partial<HeaderBody>,
  ): Promise<HeaderBody | undefined> {
    try {
      const res = await axios.patch<HeaderBody>(`${this.headerBodyPath}/${id}`, headerBody);
      globalStore.Loading();
      console.log('HeaderBody updated:', res.data);
      return res.data;
    } catch {
      showError('ไม่สามารถอัปเดต Header Body ได้');
      return;
    }
  }

  async deleteHeaderBody(id: number): Promise<HeaderBody | undefined> {
    try {
      const res = await axios.delete<HeaderBody>(`${this.headerBodyPath}/${id}`);
      Notify.create({ message: 'ลบ Header Body เรียบร้อยแล้ว', type: 'positive' });
      return res.data;
    } catch {
      showError('ไม่สามารถลบ Header Body ได้');
      return;
    }
  }

  // ==================== QUESTION API METHODS ====================

  async createQuestion(question: Omit<Question, 'id'>): Promise<Question | undefined> {
    try {
      console.log('Creating question with data:', question);

      // Try evaluate-specific endpoint first
      if (this.path.includes('evaluate')) {
        try {
          const res = await axios.post<Question>('/evaluate/questions', question);
          console.log('Question created via evaluate endpoint:', res.data);
          globalStore.Loading();
          return res.data;
        } catch (error) {
          console.log('Evaluate endpoint failed, trying alternatives...');
        }
      }

      // Fallback to general endpoint
      const res = await axios.post<Question>('/questions', question);
      console.log('Question created via general endpoint:', res.data);
      globalStore.Loading();
      return res.data;
    } catch (error) {
      console.error('Failed to create question:', error);
      showError('ไม่สามารถสร้างคำถามได้');
      return;
    }
  }

  async updateQuestion(id: number, question: Partial<Question>): Promise<Question | undefined> {
    try {
      console.log('Updating question:', { id, question });

      // Try evaluate-specific endpoint first
      if (this.path.includes('evaluate')) {
        try {
          const res = await axios.patch<Question>(`/evaluate/questions/${id}`, question);
          console.log('Question updated via evaluate endpoint:', res.data);
          globalStore.Loading();
          return res.data;
        } catch (error) {
          console.log('Evaluate endpoint failed, trying alternatives...');
        }
      }

      // Fallback to general endpoint
      const res = await axios.patch<Question>(`/questions/${id}`, question);
      console.log('Question updated via general endpoint:', res.data);
      globalStore.Loading();
      return res.data;
    } catch (error) {
      console.error('Failed to update question:', error);
      showError('ไม่สามารถอัปเดตคำถามได้');
      return;
    }
  }

  async deleteQuestion(id: number): Promise<void> {
    try {
      console.log('Deleting question:', id);

      // Try evaluate-specific endpoint first
      if (this.path.includes('evaluate')) {
        try {
          await axios.delete(`/evaluate/questions/${id}`);
          console.log('Question deleted via evaluate endpoint');
          Notify.create({ message: 'ลบคำถามเรียบร้อยแล้ว', type: 'positive' });
          return;
        } catch (error) {
          console.log('Evaluate endpoint failed, trying alternatives...');
        }
      }

      // Fallback to general endpoint
      await axios.delete(`/questions/${id}`);
      console.log('Question deleted via general endpoint');
      Notify.create({ message: 'ลบคำถามเรียบร้อยแล้ว', type: 'positive' });
    } catch (error) {
      console.error('Failed to delete question:', error);
      showError('ไม่สามารถลบคำถามได้');
    }
  }

  async updateQuestionField(
    id: number,
    field: string,
    value: unknown,
  ): Promise<Question | undefined> {
    try {
      console.log('Updating question field:', { id, field, value });

      const payload = { field, value };

      // Try evaluate-specific endpoint first
      if (this.path.includes('evaluate')) {
        try {
          const res = await axios.patch<Question>(`/evaluate/questions/${id}/field`, payload);
          console.log('Question field updated via evaluate endpoint:', res.data);
          globalStore.Loading();
          return res.data;
        } catch (error) {
          console.log('Evaluate endpoint failed, trying alternatives...');
        }
      }

      // Fallback to general endpoint
      const res = await axios.patch<Question>(`/questions/${id}/field`, payload);
      console.log('Question field updated via general endpoint:', res.data);
      globalStore.Loading();
      return res.data;
    } catch (error) {
      console.error('Failed to update question field:', error);
      showError('ไม่สามารถอัปเดตฟิลด์คำถามได้');
      return;
    }
  }

  // ==================== OPTION API METHODS ====================

  async createOption(option: Omit<Option, 'id'>): Promise<Option | undefined> {
    try {
      console.log('Creating option with data:', option);

      // Try evaluate-specific endpoint first
      if (this.path.includes('evaluate')) {
        try {
          const res = await axios.post<Option>('/evaluate/options', option);
          console.log('Option created via evaluate endpoint:', res.data);
          globalStore.Loading();
          return res.data;
        } catch (error) {
          console.log('Evaluate endpoint failed, trying alternatives...');
        }
      }

      // Fallback to general endpoint
      const res = await axios.post<Option>('/options', option);
      console.log('Option created via general endpoint:', res.data);
      globalStore.Loading();
      return res.data;
    } catch (error) {
      console.error('Failed to create option:', error);
      showError('ไม่สามารถสร้างตัวเลือกได้');
      return;
    }
  }

  async updateOption(id: number, option: Partial<Option>): Promise<Option | undefined> {
    try {
      console.log('Updating option:', { id, option });

      // Try evaluate-specific endpoint first
      if (this.path.includes('evaluate')) {
        try {
          const res = await axios.patch<Option>(`/evaluate/options/${id}`, option);
          console.log('Option updated via evaluate endpoint:', res.data);
          globalStore.Loading();
          return res.data;
        } catch (error) {
          console.log('Evaluate endpoint failed, trying alternatives...');
        }
      }

      // Fallback to general endpoint
      const res = await axios.patch<Option>(`/options/${id}`, option);
      console.log('Option updated via general endpoint:', res.data);
      globalStore.Loading();
      return res.data;
    } catch (error) {
      console.error('Failed to update option:', error);
      showError('ไม่สามารถอัปเดตตัวเลือกได้');
      return;
    }
  }

  async deleteOption(id: number): Promise<void> {
    try {
      console.log('Deleting option:', id);

      // Try evaluate-specific endpoint first
      if (this.path.includes('evaluate')) {
        try {
          await axios.delete(`/evaluate/options/${id}`);
          console.log('Option deleted via evaluate endpoint');
          Notify.create({ message: 'ลบตัวเลือกเรียบร้อยแล้ว', type: 'positive' });
          return;
        } catch (error) {
          console.log('Evaluate endpoint failed, trying alternatives...');
        }
      }

      // Fallback to general endpoint
      await axios.delete(`/options/${id}`);
      console.log('Option deleted via general endpoint');
      Notify.create({ message: 'ลบตัวเลือกเรียบร้อยแล้ว', type: 'positive' });
    } catch (error) {
      console.error('Failed to delete option:', error);
      showError('ไม่สามารถลบตัวเลือกได้');
    }
  }

  async updateOptionField(id: number, field: string, value: unknown): Promise<Option | undefined> {
    try {
      console.log('Updating option field:', { id, field, value });

      const payload = { field, value };

      // Try evaluate-specific endpoint first
      if (this.path.includes('evaluate')) {
        try {
          const res = await axios.patch<Option>(`/evaluate/options/${id}/field`, payload);
          console.log('Option field updated via evaluate endpoint:', res.data);
          globalStore.Loading();
          return res.data;
        } catch (error) {
          console.log('Evaluate endpoint failed, trying alternatives...');
        }
      }

      // Fallback to general endpoint
      const res = await axios.patch<Option>(`/options/${id}/field`, payload);
      console.log('Option field updated via general endpoint:', res.data);
      globalStore.Loading();
      return res.data;
    } catch (error) {
      console.error('Failed to update option field:', error);
      showError('ไม่สามารถอัปเดตฟิลด์ตัวเลือกได้');
      return;
    }
  }

  // ==================== BULK UPDATE METHODS ====================

  async updateItemBlockQuestions(
    itemBlockId: number,
    questions: Question[],
  ): Promise<Question[] | undefined> {
    try {
      console.log('Updating item block questions:', { itemBlockId, questions });

      // Try evaluate-specific endpoint first
      if (this.path.includes('evaluate')) {
        try {
          const res = await axios.patch<Question[]>(
            `/evaluate/item-blocks/${itemBlockId}/questions`,
            { questions },
          );
          console.log('Questions updated via evaluate endpoint:', res.data);
          globalStore.Loading();
          return res.data;
        } catch (error) {
          console.log('Evaluate endpoint failed, trying alternatives...');
        }
      }

      // Fallback to general endpoint
      const res = await axios.patch<Question[]>(`/item-blocks/${itemBlockId}/questions`, {
        questions,
      });
      console.log('Questions updated via general endpoint:', res.data);
      globalStore.Loading();
      return res.data;
    } catch (error) {
      console.error('Failed to update item block questions:', error);
      showError('ไม่สามารถอัปเดตคำถามในบล็อกได้');
      return;
    }
  }

  async updateItemBlockOptions(
    itemBlockId: number,
    options: Option[],
  ): Promise<Option[] | undefined> {
    try {
      console.log('Updating item block options:', { itemBlockId, options });

      // Try evaluate-specific endpoint first
      if (this.path.includes('evaluate')) {
        try {
          const res = await axios.patch<Option[]>(`/evaluate/item-blocks/${itemBlockId}/options`, {
            options,
          });
          console.log('Options updated via evaluate endpoint:', res.data);
          globalStore.Loading();
          return res.data;
        } catch (error) {
          console.log('Evaluate endpoint failed, trying alternatives...');
        }
      }

      // Fallback to general endpoint
      const res = await axios.patch<Option[]>(`/item-blocks/${itemBlockId}/options`, { options });
      console.log('Options updated via general endpoint:', res.data);
      globalStore.Loading();
      return res.data;
    } catch (error) {
      console.error('Failed to update item block options:', error);
      showError('ไม่สามารถอัปเดตตัวเลือกในบล็อกได้');
      return;
    }
  }
}
