import { AssessmentService } from 'src/services/quiz/assessmentService';
import type { Question, Option } from 'src/types/models';

/**
 * Service for managing Questions and Options in the evaluate system
 * This is a wrapper around AssessmentService to provide easier access to question/option APIs
 */
export class QuestionOptionService {
  private assessmentService: AssessmentService;

  constructor() {
    this.assessmentService = new AssessmentService('evaluate');
  }

  // ==================== QUESTION METHODS ====================

  /**
   * Create a new question in an item block
   */
  async createQuestion(questionData: {
    itemBlockId: number;
    questionText: string;
    isHeader?: boolean;
    sequence?: number;
    score?: number;
    imagePath?: string;
    imageWidth?: number;
    imageHeight?: number;
    sizeLimit?: number;
    acceptFile?: string;
    uploadLimit?: number;
  }): Promise<Question | undefined> {
    const question: Omit<Question, 'id'> = {
      itemBlockId: questionData.itemBlockId,
      questionText: questionData.questionText,
      isHeader: questionData.isHeader ?? false,
      sequence: questionData.sequence ?? 1,
      score: questionData.score ?? 0,
      imagePath: questionData.imagePath,
      imageWidth: questionData.imageWidth,
      imageHeight: questionData.imageHeight,
      sizeLimit: questionData.sizeLimit,
      acceptFile: questionData.acceptFile,
      uploadLimit: questionData.uploadLimit,
    };

    return await this.assessmentService.createQuestion(question);
  }

  /**
   * Update an existing question
   */
  async updateQuestion(id: number, updates: Partial<Question>): Promise<Question | undefined> {
    return await this.assessmentService.updateQuestion(id, updates);
  }

  /**
   * Update a specific field of a question
   */
  async updateQuestionField(id: number, field: keyof Question, value: unknown): Promise<Question | undefined> {
    return await this.assessmentService.updateQuestionField(id, field as string, value);
  }

  /**
   * Delete a question
   */
  async deleteQuestion(id: number): Promise<void> {
    return await this.assessmentService.deleteQuestion(id);
  }

  // ==================== OPTION METHODS ====================

  /**
   * Create a new option in an item block
   */
  async createOption(optionData: {
    itemBlockId: number;
    optionText: string;
    value?: number;
    sequence?: number;
    imagePath?: string;
    nextSection?: number;
  }): Promise<Option | undefined> {
    const option: Omit<Option, 'id'> = {
      itemBlockId: optionData.itemBlockId,
      optionText: optionData.optionText,
      value: optionData.value ?? 0,
      sequence: optionData.sequence ?? 1,
      imagePath: optionData.imagePath,
      nextSection: optionData.nextSection,
    };

    return await this.assessmentService.createOption(option);
  }

  /**
   * Update an existing option
   */
  async updateOption(id: number, updates: Partial<Option>): Promise<Option | undefined> {
    return await this.assessmentService.updateOption(id, updates);
  }

  /**
   * Update a specific field of an option
   */
  async updateOptionField(id: number, field: keyof Option, value: unknown): Promise<Option | undefined> {
    return await this.assessmentService.updateOptionField(id, field as string, value);
  }

  /**
   * Delete an option
   */
  async deleteOption(id: number): Promise<void> {
    return await this.assessmentService.deleteOption(id);
  }

  // ==================== BULK UPDATE METHODS ====================

  /**
   * Update all questions in an item block
   */
  async updateItemBlockQuestions(itemBlockId: number, questions: Question[]): Promise<Question[] | undefined> {
    return await this.assessmentService.updateItemBlockQuestions(itemBlockId, questions);
  }

  /**
   * Update all options in an item block
   */
  async updateItemBlockOptions(itemBlockId: number, options: Option[]): Promise<Option[] | undefined> {
    return await this.assessmentService.updateItemBlockOptions(itemBlockId, options);
  }

  // ==================== CONVENIENCE METHODS ====================

  /**
   * Add a new option to an existing item block
   */
  async addOptionToItemBlock(itemBlockId: number, optionText: string, sequence?: number): Promise<Option | undefined> {
    return await this.createOption({
      itemBlockId,
      optionText,
      sequence: sequence ?? 1,
      value: 0,
    });
  }

  /**
   * Add a new question to an existing item block
   */
  async addQuestionToItemBlock(itemBlockId: number, questionText: string, sequence?: number): Promise<Question | undefined> {
    return await this.createQuestion({
      itemBlockId,
      questionText,
      sequence: sequence ?? 1,
      isHeader: false,
      score: 0,
    });
  }

  /**
   * Update question text
   */
  async updateQuestionText(questionId: number, questionText: string): Promise<Question | undefined> {
    return await this.updateQuestionField(questionId, 'questionText', questionText);
  }

  /**
   * Update option text
   */
  async updateOptionText(optionId: number, optionText: string): Promise<Option | undefined> {
    return await this.updateOptionField(optionId, 'optionText', optionText);
  }

  /**
   * Update question score
   */
  async updateQuestionScore(questionId: number, score: number): Promise<Question | undefined> {
    return await this.updateQuestionField(questionId, 'score', score);
  }

  /**
   * Update option value
   */
  async updateOptionValue(optionId: number, value: number): Promise<Option | undefined> {
    return await this.updateOptionField(optionId, 'value', value);
  }

  /**
   * Set question as header
   */
  async setQuestionAsHeader(questionId: number, isHeader: boolean = true): Promise<Question | undefined> {
    return await this.updateQuestionField(questionId, 'isHeader', isHeader);
  }

  /**
   * Update question sequence
   */
  async updateQuestionSequence(questionId: number, sequence: number): Promise<Question | undefined> {
    return await this.updateQuestionField(questionId, 'sequence', sequence);
  }

  /**
   * Update option sequence
   */
  async updateOptionSequence(optionId: number, sequence: number): Promise<Option | undefined> {
    return await this.updateOptionField(optionId, 'sequence', sequence);
  }
}

// Export a default instance for easy use
export const questionOptionService = new QuestionOptionService();

// Export the class for custom instances
export default QuestionOptionService;
