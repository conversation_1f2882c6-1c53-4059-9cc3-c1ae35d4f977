<template>
  <div class="q-pa-md">
    <h4>Question & Option API Usage Examples</h4>
    
    <!-- Question Management -->
    <q-card class="q-mb-md">
      <q-card-section>
        <h6>Question Management</h6>
        <q-input v-model="newQuestionText" label="Question Text" />
        <q-btn @click="createQuestion" color="primary" class="q-mt-sm">
          Create Question
        </q-btn>
        <q-btn @click="updateQuestionText" color="secondary" class="q-mt-sm q-ml-sm">
          Update Question Text
        </q-btn>
        <q-btn @click="deleteQuestion" color="negative" class="q-mt-sm q-ml-sm">
          Delete Question
        </q-btn>
      </q-card-section>
    </q-card>

    <!-- Option Management -->
    <q-card class="q-mb-md">
      <q-card-section>
        <h6>Option Management</h6>
        <q-input v-model="newOptionText" label="Option Text" />
        <q-btn @click="createOption" color="primary" class="q-mt-sm">
          Create Option
        </q-btn>
        <q-btn @click="updateOptionText" color="secondary" class="q-mt-sm q-ml-sm">
          Update Option Text
        </q-btn>
        <q-btn @click="deleteOption" color="negative" class="q-mt-sm q-ml-sm">
          Delete Option
        </q-btn>
      </q-card-section>
    </q-card>

    <!-- Results Display -->
    <q-card>
      <q-card-section>
        <h6>API Results</h6>
        <pre>{{ JSON.stringify(apiResults, null, 2) }}</pre>
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { questionOptionService } from 'src/services/evaluate/questionOptionService';
import type { Question, Option } from 'src/types/models';

// Reactive data
const newQuestionText = ref('Sample Question');
const newOptionText = ref('Sample Option');
const apiResults = ref<any>({});

// Example item block ID (you would get this from your actual item block)
const itemBlockId = ref(1);
const questionId = ref(1);
const optionId = ref(1);

// ==================== QUESTION API EXAMPLES ====================

/**
 * Example: Create a new question
 */
const createQuestion = async () => {
  try {
    const result = await questionOptionService.createQuestion({
      itemBlockId: itemBlockId.value,
      questionText: newQuestionText.value,
      isHeader: false,
      sequence: 1,
      score: 10,
    });
    
    apiResults.value.createQuestion = result;
    console.log('Question created:', result);
    
    if (result?.id) {
      questionId.value = result.id;
    }
  } catch (error) {
    console.error('Failed to create question:', error);
    apiResults.value.createQuestionError = error;
  }
};

/**
 * Example: Update question text
 */
const updateQuestionText = async () => {
  try {
    const result = await questionOptionService.updateQuestionText(
      questionId.value,
      `Updated: ${newQuestionText.value}`
    );
    
    apiResults.value.updateQuestionText = result;
    console.log('Question text updated:', result);
  } catch (error) {
    console.error('Failed to update question text:', error);
    apiResults.value.updateQuestionTextError = error;
  }
};

/**
 * Example: Delete a question
 */
const deleteQuestion = async () => {
  try {
    await questionOptionService.deleteQuestion(questionId.value);
    
    apiResults.value.deleteQuestion = 'Question deleted successfully';
    console.log('Question deleted');
  } catch (error) {
    console.error('Failed to delete question:', error);
    apiResults.value.deleteQuestionError = error;
  }
};

// ==================== OPTION API EXAMPLES ====================

/**
 * Example: Create a new option
 */
const createOption = async () => {
  try {
    const result = await questionOptionService.createOption({
      itemBlockId: itemBlockId.value,
      optionText: newOptionText.value,
      value: 1,
      sequence: 1,
    });
    
    apiResults.value.createOption = result;
    console.log('Option created:', result);
    
    if (result?.id) {
      optionId.value = result.id;
    }
  } catch (error) {
    console.error('Failed to create option:', error);
    apiResults.value.createOptionError = error;
  }
};

/**
 * Example: Update option text
 */
const updateOptionText = async () => {
  try {
    const result = await questionOptionService.updateOptionText(
      optionId.value,
      `Updated: ${newOptionText.value}`
    );
    
    apiResults.value.updateOptionText = result;
    console.log('Option text updated:', result);
  } catch (error) {
    console.error('Failed to update option text:', error);
    apiResults.value.updateOptionTextError = error;
  }
};

/**
 * Example: Delete an option
 */
const deleteOption = async () => {
  try {
    await questionOptionService.deleteOption(optionId.value);
    
    apiResults.value.deleteOption = 'Option deleted successfully';
    console.log('Option deleted');
  } catch (error) {
    console.error('Failed to delete option:', error);
    apiResults.value.deleteOptionError = error;
  }
};

// ==================== ADVANCED EXAMPLES ====================

/**
 * Example: Bulk update questions in an item block
 */
const bulkUpdateQuestions = async () => {
  try {
    const questions: Question[] = [
      {
        id: 1,
        itemBlockId: itemBlockId.value,
        questionText: 'Updated Question 1',
        isHeader: false,
        sequence: 1,
        score: 10,
      },
      {
        id: 2,
        itemBlockId: itemBlockId.value,
        questionText: 'Updated Question 2',
        isHeader: false,
        sequence: 2,
        score: 15,
      },
    ];
    
    const result = await questionOptionService.updateItemBlockQuestions(
      itemBlockId.value,
      questions
    );
    
    apiResults.value.bulkUpdateQuestions = result;
    console.log('Questions bulk updated:', result);
  } catch (error) {
    console.error('Failed to bulk update questions:', error);
    apiResults.value.bulkUpdateQuestionsError = error;
  }
};

/**
 * Example: Bulk update options in an item block
 */
const bulkUpdateOptions = async () => {
  try {
    const options: Option[] = [
      {
        id: 1,
        itemBlockId: itemBlockId.value,
        optionText: 'Updated Option 1',
        value: 1,
        sequence: 1,
      },
      {
        id: 2,
        itemBlockId: itemBlockId.value,
        optionText: 'Updated Option 2',
        value: 2,
        sequence: 2,
      },
    ];
    
    const result = await questionOptionService.updateItemBlockOptions(
      itemBlockId.value,
      options
    );
    
    apiResults.value.bulkUpdateOptions = result;
    console.log('Options bulk updated:', result);
  } catch (error) {
    console.error('Failed to bulk update options:', error);
    apiResults.value.bulkUpdateOptionsError = error;
  }
};

/**
 * Example: Update specific question fields
 */
const updateQuestionFields = async () => {
  try {
    // Update question score
    const scoreResult = await questionOptionService.updateQuestionScore(questionId.value, 25);
    
    // Set question as header
    const headerResult = await questionOptionService.setQuestionAsHeader(questionId.value, true);
    
    // Update question sequence
    const sequenceResult = await questionOptionService.updateQuestionSequence(questionId.value, 3);
    
    apiResults.value.updateQuestionFields = {
      score: scoreResult,
      header: headerResult,
      sequence: sequenceResult,
    };
    
    console.log('Question fields updated');
  } catch (error) {
    console.error('Failed to update question fields:', error);
    apiResults.value.updateQuestionFieldsError = error;
  }
};
</script>

<style scoped>
pre {
  background-color: #f5f5f5;
  padding: 1rem;
  border-radius: 4px;
  max-height: 300px;
  overflow-y: auto;
}
</style>
