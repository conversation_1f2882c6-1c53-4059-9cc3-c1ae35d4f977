<template>
  <div class="item-block-editor">
    <q-card class="q-mb-md">
      <q-card-section>
        <h6>ItemBlock Editor - Questions & Options</h6>
        <p>ItemBlock ID: {{ itemBlockId }}</p>
      </q-card-section>
    </q-card>

    <!-- Questions Section -->
    <q-card class="q-mb-md">
      <q-card-section>
        <div class="row items-center justify-between">
          <h6>Questions</h6>
          <q-btn 
            color="primary" 
            icon="add" 
            label="Add Question" 
            @click="addNewQuestion"
            :loading="loading.addQuestion"
          />
        </div>
        
        <div v-for="(question, index) in questions" :key="question.id" class="q-mt-md">
          <q-input
            v-model="question.questionText"
            :label="`Question ${index + 1}`"
            @blur="updateQuestionText(question.id, question.questionText)"
            outlined
            class="q-mb-sm"
          />
          
          <div class="row q-gutter-sm">
            <q-input
              v-model.number="question.score"
              label="Score"
              type="number"
              @blur="updateQuestionScore(question.id, question.score)"
              style="width: 100px"
            />
            
            <q-checkbox
              v-model="question.isHeader"
              label="Header Question"
              @update:model-value="updateQuestionHeader(question.id, question.isHeader)"
            />
            
            <q-btn
              color="negative"
              icon="delete"
              size="sm"
              @click="deleteQuestionById(question.id)"
              :loading="loading.deleteQuestion === question.id"
            />
          </div>
        </div>
      </q-card-section>
    </q-card>

    <!-- Options Section -->
    <q-card class="q-mb-md">
      <q-card-section>
        <div class="row items-center justify-between">
          <h6>Options</h6>
          <q-btn 
            color="primary" 
            icon="add" 
            label="Add Option" 
            @click="addNewOption"
            :loading="loading.addOption"
          />
        </div>
        
        <div v-for="(option, index) in options" :key="option.id" class="q-mt-md">
          <q-input
            v-model="option.optionText"
            :label="`Option ${index + 1}`"
            @blur="updateOptionText(option.id, option.optionText)"
            outlined
            class="q-mb-sm"
          />
          
          <div class="row q-gutter-sm">
            <q-input
              v-model.number="option.value"
              label="Value"
              type="number"
              @blur="updateOptionValue(option.id, option.value)"
              style="width: 100px"
            />
            
            <q-input
              v-model.number="option.sequence"
              label="Sequence"
              type="number"
              @blur="updateOptionSequence(option.id, option.sequence)"
              style="width: 100px"
            />
            
            <q-btn
              color="negative"
              icon="delete"
              size="sm"
              @click="deleteOptionById(option.id)"
              :loading="loading.deleteOption === option.id"
            />
          </div>
        </div>
      </q-card-section>
    </q-card>

    <!-- Bulk Actions -->
    <q-card>
      <q-card-section>
        <h6>Bulk Actions</h6>
        <div class="row q-gutter-sm">
          <q-btn
            color="secondary"
            label="Save All Questions"
            @click="saveAllQuestions"
            :loading="loading.saveAllQuestions"
          />
          <q-btn
            color="secondary"
            label="Save All Options"
            @click="saveAllOptions"
            :loading="loading.saveAllOptions"
          />
        </div>
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { questionOptionService } from 'src/services/evaluate/questionOptionService';
import type { Question, Option } from 'src/types/models';
import { debounce } from 'lodash-es';

// Props
const props = defineProps<{
  itemBlockId: number;
  initialQuestions?: Question[];
  initialOptions?: Option[];
}>();

// Reactive data
const questions = ref<Question[]>([]);
const options = ref<Option[]>([]);

// Loading states
const loading = reactive({
  addQuestion: false,
  addOption: false,
  deleteQuestion: null as number | null,
  deleteOption: null as number | null,
  saveAllQuestions: false,
  saveAllOptions: false,
});

// Initialize data
onMounted(() => {
  if (props.initialQuestions) {
    questions.value = [...props.initialQuestions];
  }
  if (props.initialOptions) {
    options.value = [...props.initialOptions];
  }
});

// ==================== QUESTION METHODS ====================

const addNewQuestion = async () => {
  loading.addQuestion = true;
  try {
    const newQuestion = await questionOptionService.createQuestion({
      itemBlockId: props.itemBlockId,
      questionText: 'New Question',
      sequence: questions.value.length + 1,
      score: 0,
      isHeader: false,
    });

    if (newQuestion) {
      questions.value.push(newQuestion);
    }
  } catch (error) {
    console.error('Failed to add question:', error);
  } finally {
    loading.addQuestion = false;
  }
};

// Debounced update functions
const debouncedUpdateQuestionText = debounce(async (questionId: number, text: string) => {
  try {
    await questionOptionService.updateQuestionText(questionId, text);
    console.log('Question text updated');
  } catch (error) {
    console.error('Failed to update question text:', error);
  }
}, 500);

const debouncedUpdateQuestionScore = debounce(async (questionId: number, score: number) => {
  try {
    await questionOptionService.updateQuestionScore(questionId, score);
    console.log('Question score updated');
  } catch (error) {
    console.error('Failed to update question score:', error);
  }
}, 500);

const updateQuestionText = (questionId: number, text: string) => {
  debouncedUpdateQuestionText(questionId, text);
};

const updateQuestionScore = (questionId: number, score: number) => {
  debouncedUpdateQuestionScore(questionId, score);
};

const updateQuestionHeader = async (questionId: number, isHeader: boolean) => {
  try {
    await questionOptionService.setQuestionAsHeader(questionId, isHeader);
    console.log('Question header status updated');
  } catch (error) {
    console.error('Failed to update question header status:', error);
  }
};

const deleteQuestionById = async (questionId: number) => {
  loading.deleteQuestion = questionId;
  try {
    await questionOptionService.deleteQuestion(questionId);
    questions.value = questions.value.filter(q => q.id !== questionId);
    console.log('Question deleted');
  } catch (error) {
    console.error('Failed to delete question:', error);
  } finally {
    loading.deleteQuestion = null;
  }
};

// ==================== OPTION METHODS ====================

const addNewOption = async () => {
  loading.addOption = true;
  try {
    const newOption = await questionOptionService.createOption({
      itemBlockId: props.itemBlockId,
      optionText: 'New Option',
      sequence: options.value.length + 1,
      value: options.value.length + 1,
    });

    if (newOption) {
      options.value.push(newOption);
    }
  } catch (error) {
    console.error('Failed to add option:', error);
  } finally {
    loading.addOption = false;
  }
};

// Debounced update functions for options
const debouncedUpdateOptionText = debounce(async (optionId: number, text: string) => {
  try {
    await questionOptionService.updateOptionText(optionId, text);
    console.log('Option text updated');
  } catch (error) {
    console.error('Failed to update option text:', error);
  }
}, 500);

const debouncedUpdateOptionValue = debounce(async (optionId: number, value: number) => {
  try {
    await questionOptionService.updateOptionValue(optionId, value);
    console.log('Option value updated');
  } catch (error) {
    console.error('Failed to update option value:', error);
  }
}, 500);

const debouncedUpdateOptionSequence = debounce(async (optionId: number, sequence: number) => {
  try {
    await questionOptionService.updateOptionSequence(optionId, sequence);
    console.log('Option sequence updated');
  } catch (error) {
    console.error('Failed to update option sequence:', error);
  }
}, 500);

const updateOptionText = (optionId: number, text: string) => {
  debouncedUpdateOptionText(optionId, text);
};

const updateOptionValue = (optionId: number, value: number) => {
  debouncedUpdateOptionValue(optionId, value);
};

const updateOptionSequence = (optionId: number, sequence: number) => {
  debouncedUpdateOptionSequence(optionId, sequence);
};

const deleteOptionById = async (optionId: number) => {
  loading.deleteOption = optionId;
  try {
    await questionOptionService.deleteOption(optionId);
    options.value = options.value.filter(o => o.id !== optionId);
    console.log('Option deleted');
  } catch (error) {
    console.error('Failed to delete option:', error);
  } finally {
    loading.deleteOption = null;
  }
};

// ==================== BULK OPERATIONS ====================

const saveAllQuestions = async () => {
  loading.saveAllQuestions = true;
  try {
    const result = await questionOptionService.updateItemBlockQuestions(
      props.itemBlockId,
      questions.value
    );
    
    if (result) {
      questions.value = result;
      console.log('All questions saved');
    }
  } catch (error) {
    console.error('Failed to save all questions:', error);
  } finally {
    loading.saveAllQuestions = false;
  }
};

const saveAllOptions = async () => {
  loading.saveAllOptions = true;
  try {
    const result = await questionOptionService.updateItemBlockOptions(
      props.itemBlockId,
      options.value
    );
    
    if (result) {
      options.value = result;
      console.log('All options saved');
    }
  } catch (error) {
    console.error('Failed to save all options:', error);
  } finally {
    loading.saveAllOptions = false;
  }
};
</script>

<style scoped>
.item-block-editor {
  max-width: 800px;
  margin: 0 auto;
}
</style>
