<template>
  <q-drawer
    :mini="miniState"
    @mouseenter="miniState = false"
    @mouseleave="miniState = true"
    :breakpoint="500"
    show-if-above
    v-model:model-value="globalStore.leftDrawerState"
    side="left"
    bordered
  >
    <div class="column full-height">
      \
      <div>
        <q-list>
          <q-item v-for="item in menu" :key="item.title" clickable :to="item.link" exact>
            <q-item-section avatar>
              <q-icon :name="item.icon" />
            </q-item-section>
            <q-item-section>
              <q-item-label class="text-body1">{{ item.title }}</q-item-label>
            </q-item-section>
          </q-item>
        </q-list>
      </div>

      <q-space />

      <q-separator />

      <q-list>
        <q-item v-if="logOutMenu?.[0]" clickable :to="logOutMenu?.[0]?.link" exact>
          <q-item-section avatar>
            <q-icon color="red" :name="logOutMenu?.[0]?.icon" />
          </q-item-section>
          <q-item-section>
            <q-item-label class="text-red text-body1">{{ logOutMenu?.[0]?.title }}</q-item-label>
          </q-item-section>
        </q-item>
      </q-list>
    </div>
  </q-drawer>
</template>

<script setup lang="ts">
import { useGlobalStore } from 'src/stores/global';
import type { MenuLink } from 'src/types/app';
import { ref } from 'vue';
import { LogOutMenu } from 'src/data/menu';

const globalStore = useGlobalStore();
const miniState = ref(true);

defineProps<{
  menu: MenuLink[];
}>();

const logOutMenu = LogOutMenu;
</script>
