<template>
  <q-drawer
    :mini="miniState"
    @mouseenter="miniState = false"
    @mouseleave="miniState = true"
    :breakpoint="500"
    show-if-above
    v-model:model-value="globalStore.leftDrawerState"
    side="left"
    bordered
  >
    <!-- drawer content -->
    <q-list v-for="item in menu" :key="item.title">
      <q-item clickable :to="item.link" exact>
        <q-item-section avatar>
          <q-icon :name="item.icon"></q-icon>
        </q-item-section>
        <q-item-section>
          <q-item-label class="text-body1">{{ item.title }}</q-item-label>
        </q-item-section>
      </q-item>
    </q-list>
  </q-drawer>
</template>

<script setup lang="ts">
import { useGlobalStore } from 'src/stores/global';
import type { MenuLink } from 'src/types/app';
import { ref } from 'vue';

const globalStore = useGlobalStore();
const miniState = ref(true);

defineProps<{
  menu: MenuLink[];
}>();
</script>

<style scoped></style>
