<template>
  <q-card class="q-pa-md q-ma-md evaluate-get">
    <div class="row q-ma-md">
      <q-markdown class="title">
        {{ matchedItem?.questions?.[0]?.questionText || 'ไม่พบคำถาม' }}
      </q-markdown>
      <div class="required">*</div>
    </div>

    <div class="row q-ma-md">
      <div v-if="category === 'multiple choice'" class="group font-size">
        <q-radio
          v-model="selectedAnswer"
          v-for="option in matchedItem?.options?.filter((o) => o.itemBlockId === matchedItem?.id)"
          :key="option.id ?? option.optionText"
          :val="option.optionText"
          :label="option.optionText"
          color="primary"
          @update:model-value="emitAnswer"
          @blur="handleBlur"
        />
      </div>

      <div v-else-if="category === 'checkbox'" class="group font-size">
        <q-checkbox
          v-for="choice in matchedItem?.options?.filter((o) => o.itemBlockId === matchedItem?.id)"
          v-model="selectedAnswers"
          :key="choice.id ?? choice.optionText"
          :val="choice.optionText"
          :label="choice.optionText"
          color="primary"
          @update:model-value="emitAnswer"
        />
      </div>

      <div v-else-if="category === 'shortAnswer'">
        <q-input
          v-model="selectedAnswer"
          dense
          placeholder="คำตอบ..."
          style="min-width: 400px"
          class="font-size"
          @update:model-value="emitAnswer"
        />
      </div>

      <div v-else-if="category === 'multipleGridChoice'" class="grid-choice">
        <q-table flat bordered :rows="rows" :columns="columns" row-key="id" hide-bottom>
          <template v-slot:body-cell="props">
            <q-td :props="props">
              <template v-if="props.col.name === 'question'">
                {{ props.row.question }}
              </template>
              <template v-else>
                <q-radio
                  :val="Number(props.col.name.replace('choice_', ''))"
                  v-model="selectedAnswersGrid[props.row.id]"
                  size="sm"
                  color="primary"
                />
              </template>
            </q-td>
          </template>
        </q-table>
      </div>
      <!--
      <div v-else-if="category === 'fileUpload'">
        <div class="q-mb-md" style="color: #9d9d9d">
          {{
            'อัปโหลด ' +
            item.evaluateQuestions[0]?.limitFile +
            ' ไฟล์ ' +
            item.evaluateQuestions[0]?.categoryFile +
            ' มากสุด ' +
            item.evaluateQuestions[0]?.sizeImage
          }}
        </div>
        <MyButton icon="file_upload" label="เพิ่มไฟล์" class="file-upload font-size" />
      </div> -->
    </div>
  </q-card>
</template>

<script setup lang="ts">
import type { QTableColumn } from 'quasar';
import type { Assessment } from 'src/types/models';
// import MyButton from 'src/components/common/MyButton.vue';
// import type { EvaluateItem } from 'src/types/evaluate/evaluateItem';
import { computed, onMounted, reactive, ref, watch } from 'vue';

// รวมทุก type เป็น union type

// const props = defineProps<{
//   item: EvaluateItem;
//   category: 'multiple choice' | 'shortAnswer' | 'fileUpload' | 'multipleGridChoice' | 'checkbox';
// }>();

const props = defineProps<{
  id: number;
  category: string;
  item: Assessment;
}>();

const emit = defineEmits<{
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  (e: 'update-answer', answer: { type: string; value: any }): void;
}>();

// Reactive state สำหรับคำตอบ
const selectedAnswer = ref<string>('');
const selectedAnswers = ref<string[]>([]);
const gridAnswers = ref<{ [key: number]: number }>({});

// Emit คำตอบเมื่อมีการเปลี่ยนแปลง
const emitAnswer = () => {
  let answer;
  if (props.category === 'multipleGridChoice') {
    answer = { type: props.category, value: gridAnswers.value };
  } else if (props.category === 'checkbox') {
    answer = { type: props.category, value: selectedAnswers.value };
  } else {
    answer = { type: props.category, value: selectedAnswer.value };
  }
  emit('update-answer', answer);
};

const matchedItem = computed(() => props.item.itemBlocks?.find((item) => item.id === props.id));

interface Row {
  id: number;
  question: string;
  [key: `choice_${number}`]: string; // หรือ boolean ถ้าใช้ checkbox
}

const selectedAnswersGrid = reactive<Record<number, number | null>>({});

const columns = computed<QTableColumn[]>(() => {
  const base: QTableColumn[] = [
    {
      name: 'question',
      label: 'Question',
      field: 'question',
      align: 'left',
    },
  ];

  const choices: QTableColumn[] =
    matchedItem.value?.options?.map((opt, i) => ({
      name: `choice_${i}`,
      label: opt.optionText,
      field: `choice_${i}`,
      align: 'center',
    })) ?? [];

  return base.concat(choices);
});

const rows = computed<Row[]>(() => {
  if (!matchedItem.value?.questions) return [];

  return matchedItem.value.questions.map((q) => {
    const row: Row = {
      id: q.id,
      question: q.questionText,
    };

    matchedItem.value?.options?.forEach((_, i) => {
      row[`choice_${i}`] = '';
    });

    return row;
  });
});

const STORAGE_KEY = 'draft-form';

onMounted(() => {
  console.log(props.item.itemBlocks?.[0]?.type + 'dassd');
  const saved = localStorage.getItem(STORAGE_KEY);
  if (saved) {
    const parsed = JSON.parse(saved);
    if (props.category === 'checkbox') {
      selectedAnswers.value = parsed;
    } else if (props.category === 'multipleGridChoice') {
      gridAnswers.value = parsed;
    } else {
      selectedAnswer.value = parsed;
    }
  }
});

// โหลด draft ที่เคยกรอกไว้ตอนโหลดหน้า
function saveDraft() {
  let value;
  if (props.category === 'checkbox') {
    value = selectedAnswers.value;
  } else if (props.category === 'multipleGridChoice') {
    value = gridAnswers.value;
  } else {
    value = selectedAnswer.value;
  }

  localStorage.setItem(STORAGE_KEY, JSON.stringify(value));
}

// blur → save (เฉพาะ shortAnswer เพราะ radio/checkbox ไม่มี blur)
function handleBlur() {
  saveDraft();
}

// Watch เพื่อ emit คำตอบเมื่อ selectedAnswers เปลี่ยนแปลง (สำหรับ multipleGridChoice)
watch(selectedAnswer, () => {
  emitAnswer();
  saveDraft();
});

watch(
  selectedAnswers,
  () => {
    emitAnswer();
    saveDraft();
  },
  { deep: true },
);

watch(
  gridAnswers,
  () => {
    emitAnswer();
    saveDraft();
  },
  { deep: true },
);

export interface Option {
  id: number;
  optionText: string;
  value: number;
  sequence: number;
  blockId: number;
}

export interface Question {
  id: number;
  questionText: string;
  isHeader: boolean;
  sequence: number;
  blockId?: number; // บาง type อาจไม่มี blockId
}

export interface ItemBlock {
  id: number;
  questionBlockId: number | null;
  position: number;
  page: number;
  required: boolean;
  type: 'shortAnswer' | 'multiple choice' | 'multipleGridChoice' | 'checkbox' | 'upload' | 'image'; // เพิ่มตาม enum ที่มี
  questions: Question[];
  options: Option[];
}

export interface FormData {
  title: string;
  itemBlocks: ItemBlock[];
}
</script>

<style scoped lang="scss">
.title {
  font-size: 20px;
}

.required {
  color: red;
  font-size: 20px;
  margin-left: 4px;
}
.group {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.file-upload {
  background-color: white;
  color: $primary;
  border: 1px solid;
  border-color: $surface;
}

.font-size {
  font-size: 18px;
}
.grid-choice {
  width: 100%;
  display: flex;
  justify-content: center;
}

.grid-table {
  width: 100%;
  max-width: 800px;
  table-layout: fixed;

  .label-column {
    width: 50%;
    max-width: 50%;
    word-break: break-all;
    white-space: normal;
    overflow-wrap: break-word;
  }

  .option-column {
    width: calc(50% / 5);
    padding: 8px;
    text-align: center;

    &:first-child {
      border-left: none;
    }
  }
}
</style>
