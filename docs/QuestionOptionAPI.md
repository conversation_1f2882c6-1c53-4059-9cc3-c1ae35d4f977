# Question & Option API Documentation

This document explains how to use the Question and Option APIs for updating ItemBlocks in the evaluate system.

## Overview

The Question and Option APIs allow you to:
- Create, update, and delete questions in ItemBlocks
- Create, update, and delete options in ItemBlocks
- Update specific fields of questions and options
- Perform bulk updates on questions and options

## Services

### 1. AssessmentService (Low-level API)
Located in `src/services/quiz/assessmentService.ts`
- Provides direct API methods for questions and options
- <PERSON>les endpoint fallbacks and error handling
- Used by higher-level services

### 2. QuestionOptionService (High-level API)
Located in `src/services/evaluate/questionOptionService.ts`
- Wrapper around AssessmentService
- Provides convenient methods with better TypeScript support
- Recommended for most use cases

## Usage Examples

### Import the Service

```typescript
import { questionOptionService } from 'src/services/evaluate/questionOptionService';
// or
import { QuestionOptionService } from 'src/services/evaluate/questionOptionService';
```

### Question Management

#### Create a Question
```typescript
const question = await questionOptionService.createQuestion({
  itemBlockId: 123,
  questionText: 'What is your favorite color?',
  isHeader: false,
  sequence: 1,
  score: 10,
});
```

#### Update a Question
```typescript
const updatedQuestion = await questionOptionService.updateQuestion(questionId, {
  questionText: 'Updated question text',
  score: 15,
});
```

#### Update Question Text Only
```typescript
const result = await questionOptionService.updateQuestionText(
  questionId, 
  'New question text'
);
```

#### Delete a Question
```typescript
await questionOptionService.deleteQuestion(questionId);
```

### Option Management

#### Create an Option
```typescript
const option = await questionOptionService.createOption({
  itemBlockId: 123,
  optionText: 'Red',
  value: 1,
  sequence: 1,
});
```

#### Update an Option
```typescript
const updatedOption = await questionOptionService.updateOption(optionId, {
  optionText: 'Updated option text',
  value: 2,
});
```

#### Update Option Text Only
```typescript
const result = await questionOptionService.updateOptionText(
  optionId, 
  'New option text'
);
```

#### Delete an Option
```typescript
await questionOptionService.deleteOption(optionId);
```

### Bulk Operations

#### Update All Questions in an ItemBlock
```typescript
const questions: Question[] = [
  {
    id: 1,
    itemBlockId: 123,
    questionText: 'Question 1',
    isHeader: false,
    sequence: 1,
    score: 10,
  },
  // ... more questions
];

const result = await questionOptionService.updateItemBlockQuestions(123, questions);
```

#### Update All Options in an ItemBlock
```typescript
const options: Option[] = [
  {
    id: 1,
    itemBlockId: 123,
    optionText: 'Option 1',
    value: 1,
    sequence: 1,
  },
  // ... more options
];

const result = await questionOptionService.updateItemBlockOptions(123, options);
```

### Convenience Methods

#### Quick Question Updates
```typescript
// Update question score
await questionOptionService.updateQuestionScore(questionId, 25);

// Set question as header
await questionOptionService.setQuestionAsHeader(questionId, true);

// Update question sequence
await questionOptionService.updateQuestionSequence(questionId, 3);
```

#### Quick Option Updates
```typescript
// Update option value
await questionOptionService.updateOptionValue(optionId, 5);

// Update option sequence
await questionOptionService.updateOptionSequence(optionId, 2);
```

## API Endpoints

The service automatically tries multiple endpoints to ensure compatibility:

### Question Endpoints
- Primary: `/evaluate/questions`
- Fallback: `/questions`

### Option Endpoints
- Primary: `/evaluate/options`
- Fallback: `/options`

### Bulk Update Endpoints
- Questions: `/evaluate/item-blocks/{id}/questions`
- Options: `/evaluate/item-blocks/{id}/options`

## Error Handling

All methods include comprehensive error handling:

```typescript
try {
  const question = await questionOptionService.createQuestion({
    itemBlockId: 123,
    questionText: 'Sample question',
  });
  console.log('Question created:', question);
} catch (error) {
  console.error('Failed to create question:', error);
  // Handle error appropriately
}
```

## Integration with Vue Components

### Using in Composition API
```typescript
import { ref } from 'vue';
import { questionOptionService } from 'src/services/evaluate/questionOptionService';

export default {
  setup() {
    const questions = ref<Question[]>([]);
    
    const addQuestion = async (itemBlockId: number, text: string) => {
      const question = await questionOptionService.createQuestion({
        itemBlockId,
        questionText: text,
      });
      
      if (question) {
        questions.value.push(question);
      }
    };
    
    return {
      questions,
      addQuestion,
    };
  }
};
```

### Auto-save on Input Changes
```typescript
import { debounce } from 'lodash-es';

const debouncedUpdateQuestion = debounce(async (questionId: number, text: string) => {
  await questionOptionService.updateQuestionText(questionId, text);
}, 500);

// Use in template
const onQuestionTextChange = (questionId: number, newText: string) => {
  debouncedUpdateQuestion(questionId, newText);
};
```

## Best Practices

1. **Use the high-level QuestionOptionService** for most operations
2. **Implement proper error handling** for all API calls
3. **Use debouncing** for auto-save functionality
4. **Validate data** before sending to the API
5. **Update local state** after successful API calls
6. **Use TypeScript types** for better development experience

## Complete Example

See `src/examples/QuestionOptionAPIUsage.vue` for a complete working example of all API methods.
